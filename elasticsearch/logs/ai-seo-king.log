[2025-06-24T12:07:20,082][INFO ][o.e.n.Node               ] [seo-node-1] version[7.17.15], pid[86945], build[default/tar/0b8ecfb4378335f4689c4223d1f1115f16bef3ba/2023-11-10T22:03:46.987399016Z], OS[Mac OS X/15.5/aarch64], JVM[Oracle Corporation/OpenJDK 64-Bit Server VM/21.0.1/21.0.1+12-29]
[2025-06-24T12:07:20,085][INFO ][o.e.n.Node               ] [seo-node-1] JVM home [/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/jdk.app/Contents/Home], using bundled JDK [true]
[2025-06-24T12:07:20,085][INFO ][o.e.n.Node               ] [seo-node-1] JVM arguments [-Xshare:auto, -Des.networkaddress.cache.ttl=60, -Des.networkaddress.cache.negative.ttl=10, -XX:+AlwaysPreTouch, -Xss1m, -Djava.awt.headless=true, -Dfile.encoding=UTF-8, -Djna.nosys=true, -XX:-OmitStackTraceInFastThrow, -XX:+ShowCodeDetailsInExceptionMessages, -Dio.netty.noUnsafe=true, -Dio.netty.noKeySetOptimization=true, -Dio.netty.recycler.maxCapacityPerThread=0, -Dio.netty.allocator.numDirectArenas=0, -Dlog4j.shutdownHookEnabled=false, -Dlog4j2.disable.jmx=true, -Dlog4j2.formatMsgNoLookups=true, -Djava.locale.providers=SPI,COMPAT, --add-opens=java.base/java.io=ALL-UNNAMED, -Djava.security.manager=allow, -XX:+UseG1GC, -Djava.io.tmpdir=/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-16989071375096480540, -XX:+HeapDumpOnOutOfMemoryError, -XX:+ExitOnOutOfMemoryError, -XX:HeapDumpPath=data, -XX:ErrorFile=logs/hs_err_pid%p.log, -Xlog:gc*,gc+age=trace,safepoint:file=logs/gc.log:utctime,pid,tags:filecount=32,filesize=64m, -Xms31744m, -Xmx31744m, -XX:MaxDirectMemorySize=16642998272, -XX:InitiatingHeapOccupancyPercent=30, -XX:G1ReservePercent=25, -Des.path.home=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15, -Des.path.conf=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/config, -Des.distribution.flavor=default, -Des.distribution.type=tar, -Des.bundled_jdk=true]
[2025-06-24T12:07:21,700][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [aggs-matrix-stats]
[2025-06-24T12:07:21,701][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [analysis-common]
[2025-06-24T12:07:21,701][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [constant-keyword]
[2025-06-24T12:07:21,701][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [frozen-indices]
[2025-06-24T12:07:21,701][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [ingest-common]
[2025-06-24T12:07:21,701][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [ingest-geoip]
[2025-06-24T12:07:21,701][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [ingest-user-agent]
[2025-06-24T12:07:21,701][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [kibana]
[2025-06-24T12:07:21,702][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [lang-expression]
[2025-06-24T12:07:21,702][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [lang-mustache]
[2025-06-24T12:07:21,702][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [lang-painless]
[2025-06-24T12:07:21,702][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [legacy-geo]
[2025-06-24T12:07:21,702][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [mapper-extras]
[2025-06-24T12:07:21,702][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [mapper-version]
[2025-06-24T12:07:21,702][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [parent-join]
[2025-06-24T12:07:21,702][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [percolator]
[2025-06-24T12:07:21,703][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [rank-eval]
[2025-06-24T12:07:21,703][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [reindex]
[2025-06-24T12:07:21,703][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [repositories-metering-api]
[2025-06-24T12:07:21,703][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [repository-encrypted]
[2025-06-24T12:07:21,703][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [repository-url]
[2025-06-24T12:07:21,703][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [runtime-fields-common]
[2025-06-24T12:07:21,703][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [search-business-rules]
[2025-06-24T12:07:21,703][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [searchable-snapshots]
[2025-06-24T12:07:21,704][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [snapshot-repo-test-kit]
[2025-06-24T12:07:21,704][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [spatial]
[2025-06-24T12:07:21,704][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [transform]
[2025-06-24T12:07:21,704][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [transport-netty4]
[2025-06-24T12:07:21,704][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [unsigned-long]
[2025-06-24T12:07:21,704][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [vector-tile]
[2025-06-24T12:07:21,705][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [vectors]
[2025-06-24T12:07:21,705][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [wildcard]
[2025-06-24T12:07:21,705][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-aggregate-metric]
[2025-06-24T12:07:21,705][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-analytics]
[2025-06-24T12:07:21,705][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-async]
[2025-06-24T12:07:21,705][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-async-search]
[2025-06-24T12:07:21,705][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-autoscaling]
[2025-06-24T12:07:21,705][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ccr]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-core]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-data-streams]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-deprecation]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-enrich]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-eql]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-fleet]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-graph]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-identity-provider]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ilm]
[2025-06-24T12:07:21,706][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-logstash]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ml]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-monitoring]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ql]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-rollup]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-security]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-shutdown]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-sql]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-stack]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-text-structure]
[2025-06-24T12:07:21,707][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-voting-only-node]
[2025-06-24T12:07:21,708][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-watcher]
[2025-06-24T12:07:21,708][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded plugin [analysis-icu]
[2025-06-24T12:07:21,708][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded plugin [analysis-smartcn]
[2025-06-24T12:07:21,728][INFO ][o.e.e.NodeEnvironment    ] [seo-node-1] using [1] data paths, mounts [[/System/Volumes/Data (/dev/disk3s5)]], net usable_space [231.4gb], net total_space [926.3gb], types [apfs]
[2025-06-24T12:07:21,728][INFO ][o.e.e.NodeEnvironment    ] [seo-node-1] heap size [31gb], compressed ordinary object pointers [true]
[2025-06-24T12:07:21,743][INFO ][o.e.n.Node               ] [seo-node-1] node name [seo-node-1], node ID [PdaVxQnMS_iyma-tW5xrGA], cluster name [ai-seo-king], roles [master, data, ingest]
[2025-06-24T12:07:22,964][WARN ][o.e.c.s.SettingsModule   ] [seo-node-1] 
*************************************************************************************
Found index level settings on node level configuration.

Since elasticsearch 5.x index level settings can NOT be set on the nodes 
configuration like the elasticsearch.yaml, in system properties or command line 
arguments.In order to upgrade all indices the settings must be updated via the 
/${index}/_settings API. Unless all settings are dynamic all indices must be closed 
in order to apply the upgradeIndices created in the future should use index templates 
to set default values. 

Please ensure all required values are updated on all indices by executing: 

curl -XPUT 'http://localhost:9200/_all/_settings?preserve_existing=true' -d '{
  "index.number_of_replicas" : "0",
  "index.number_of_shards" : "1"
}'
*************************************************************************************

[2025-06-24T12:07:22,966][ERROR][o.e.b.Bootstrap          ] [seo-node-1] Exception
java.lang.IllegalArgumentException: node settings must not contain any index level settings
	at org.elasticsearch.common.settings.SettingsModule.<init>(SettingsModule.java:131) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.node.Node.<init>(Node.java:502) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.node.Node.<init>(Node.java:309) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Bootstrap$5.<init>(Bootstrap.java:234) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Bootstrap.setup(Bootstrap.java:234) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Bootstrap.init(Bootstrap.java:434) [elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Elasticsearch.init(Elasticsearch.java:169) [elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Elasticsearch.execute(Elasticsearch.java:160) [elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.cli.EnvironmentAwareCommand.execute(EnvironmentAwareCommand.java:77) [elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.cli.Command.mainWithoutErrorHandling(Command.java:112) [elasticsearch-cli-7.17.15.jar:7.17.15]
	at org.elasticsearch.cli.Command.main(Command.java:77) [elasticsearch-cli-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Elasticsearch.main(Elasticsearch.java:125) [elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Elasticsearch.main(Elasticsearch.java:80) [elasticsearch-7.17.15.jar:7.17.15]
[2025-06-24T12:07:22,969][ERROR][o.e.b.ElasticsearchUncaughtExceptionHandler] [seo-node-1] uncaught exception in thread [main]
org.elasticsearch.bootstrap.StartupException: java.lang.IllegalArgumentException: node settings must not contain any index level settings
	at org.elasticsearch.bootstrap.Elasticsearch.init(Elasticsearch.java:173) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Elasticsearch.execute(Elasticsearch.java:160) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.cli.EnvironmentAwareCommand.execute(EnvironmentAwareCommand.java:77) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.cli.Command.mainWithoutErrorHandling(Command.java:112) ~[elasticsearch-cli-7.17.15.jar:7.17.15]
	at org.elasticsearch.cli.Command.main(Command.java:77) ~[elasticsearch-cli-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Elasticsearch.main(Elasticsearch.java:125) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Elasticsearch.main(Elasticsearch.java:80) ~[elasticsearch-7.17.15.jar:7.17.15]
Caused by: java.lang.IllegalArgumentException: node settings must not contain any index level settings
	at org.elasticsearch.common.settings.SettingsModule.<init>(SettingsModule.java:131) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.node.Node.<init>(Node.java:502) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.node.Node.<init>(Node.java:309) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Bootstrap$5.<init>(Bootstrap.java:234) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Bootstrap.setup(Bootstrap.java:234) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Bootstrap.init(Bootstrap.java:434) ~[elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.bootstrap.Elasticsearch.init(Elasticsearch.java:169) ~[elasticsearch-7.17.15.jar:7.17.15]
	... 6 more
[2025-06-24T12:13:29,894][INFO ][o.e.n.Node               ] [seo-node-1] version[7.17.15], pid[92363], build[default/tar/0b8ecfb4378335f4689c4223d1f1115f16bef3ba/2023-11-10T22:03:46.987399016Z], OS[Mac OS X/15.5/aarch64], JVM[Oracle Corporation/OpenJDK 64-Bit Server VM/21.0.1/21.0.1+12-29]
[2025-06-24T12:13:29,897][INFO ][o.e.n.Node               ] [seo-node-1] JVM home [/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/jdk.app/Contents/Home], using bundled JDK [true]
[2025-06-24T12:13:29,897][INFO ][o.e.n.Node               ] [seo-node-1] JVM arguments [-Xshare:auto, -Des.networkaddress.cache.ttl=60, -Des.networkaddress.cache.negative.ttl=10, -XX:+AlwaysPreTouch, -Xss1m, -Djava.awt.headless=true, -Dfile.encoding=UTF-8, -Djna.nosys=true, -XX:-OmitStackTraceInFastThrow, -XX:+ShowCodeDetailsInExceptionMessages, -Dio.netty.noUnsafe=true, -Dio.netty.noKeySetOptimization=true, -Dio.netty.recycler.maxCapacityPerThread=0, -Dio.netty.allocator.numDirectArenas=0, -Dlog4j.shutdownHookEnabled=false, -Dlog4j2.disable.jmx=true, -Dlog4j2.formatMsgNoLookups=true, -Djava.locale.providers=SPI,COMPAT, --add-opens=java.base/java.io=ALL-UNNAMED, -Djava.security.manager=allow, -XX:+UseG1GC, -Djava.io.tmpdir=/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891, -XX:+HeapDumpOnOutOfMemoryError, -XX:+ExitOnOutOfMemoryError, -XX:HeapDumpPath=data, -XX:ErrorFile=logs/hs_err_pid%p.log, -Xlog:gc*,gc+age=trace,safepoint:file=logs/gc.log:utctime,pid,tags:filecount=32,filesize=64m, -Xms2g, -Xmx2g, -XX:MaxDirectMemorySize=1g, -XX:InitiatingHeapOccupancyPercent=30, -XX:G1ReservePercent=25, -XX:MaxGCPauseMillis=100, -XX:G1HeapRegionSize=4m, -Des.path.home=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15, -Des.path.conf=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/config, -Des.distribution.flavor=default, -Des.distribution.type=tar, -Des.bundled_jdk=true]
[2025-06-24T12:13:31,245][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [aggs-matrix-stats]
[2025-06-24T12:13:31,245][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [analysis-common]
[2025-06-24T12:13:31,245][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [constant-keyword]
[2025-06-24T12:13:31,245][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [frozen-indices]
[2025-06-24T12:13:31,246][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [ingest-common]
[2025-06-24T12:13:31,246][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [ingest-geoip]
[2025-06-24T12:13:31,246][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [ingest-user-agent]
[2025-06-24T12:13:31,246][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [kibana]
[2025-06-24T12:13:31,246][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [lang-expression]
[2025-06-24T12:13:31,246][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [lang-mustache]
[2025-06-24T12:13:31,246][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [lang-painless]
[2025-06-24T12:13:31,246][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [legacy-geo]
[2025-06-24T12:13:31,247][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [mapper-extras]
[2025-06-24T12:13:31,247][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [mapper-version]
[2025-06-24T12:13:31,247][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [parent-join]
[2025-06-24T12:13:31,247][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [percolator]
[2025-06-24T12:13:31,247][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [rank-eval]
[2025-06-24T12:13:31,247][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [reindex]
[2025-06-24T12:13:31,247][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [repositories-metering-api]
[2025-06-24T12:13:31,247][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [repository-encrypted]
[2025-06-24T12:13:31,248][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [repository-url]
[2025-06-24T12:13:31,248][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [runtime-fields-common]
[2025-06-24T12:13:31,248][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [search-business-rules]
[2025-06-24T12:13:31,248][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [searchable-snapshots]
[2025-06-24T12:13:31,248][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [snapshot-repo-test-kit]
[2025-06-24T12:13:31,248][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [spatial]
[2025-06-24T12:13:31,249][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [transform]
[2025-06-24T12:13:31,249][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [transport-netty4]
[2025-06-24T12:13:31,249][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [unsigned-long]
[2025-06-24T12:13:31,249][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [vector-tile]
[2025-06-24T12:13:31,249][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [vectors]
[2025-06-24T12:13:31,249][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [wildcard]
[2025-06-24T12:13:31,249][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-aggregate-metric]
[2025-06-24T12:13:31,249][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-analytics]
[2025-06-24T12:13:31,249][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-async]
[2025-06-24T12:13:31,250][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-async-search]
[2025-06-24T12:13:31,250][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-autoscaling]
[2025-06-24T12:13:31,250][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ccr]
[2025-06-24T12:13:31,250][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-core]
[2025-06-24T12:13:31,250][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-data-streams]
[2025-06-24T12:13:31,251][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-deprecation]
[2025-06-24T12:13:31,251][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-enrich]
[2025-06-24T12:13:31,251][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-eql]
[2025-06-24T12:13:31,251][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-fleet]
[2025-06-24T12:13:31,252][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-graph]
[2025-06-24T12:13:31,252][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-identity-provider]
[2025-06-24T12:13:31,252][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ilm]
[2025-06-24T12:13:31,252][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-logstash]
[2025-06-24T12:13:31,252][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ml]
[2025-06-24T12:13:31,252][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-monitoring]
[2025-06-24T12:13:31,252][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ql]
[2025-06-24T12:13:31,253][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-rollup]
[2025-06-24T12:13:31,253][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-security]
[2025-06-24T12:13:31,253][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-shutdown]
[2025-06-24T12:13:31,253][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-sql]
[2025-06-24T12:13:31,253][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-stack]
[2025-06-24T12:13:31,253][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-text-structure]
[2025-06-24T12:13:31,253][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-voting-only-node]
[2025-06-24T12:13:31,254][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-watcher]
[2025-06-24T12:13:31,254][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded plugin [analysis-icu]
[2025-06-24T12:13:31,254][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded plugin [analysis-smartcn]
[2025-06-24T12:13:31,272][INFO ][o.e.e.NodeEnvironment    ] [seo-node-1] using [1] data paths, mounts [[/System/Volumes/Data (/dev/disk3s5)]], net usable_space [231.3gb], net total_space [926.3gb], types [apfs]
[2025-06-24T12:13:31,273][INFO ][o.e.e.NodeEnvironment    ] [seo-node-1] heap size [2gb], compressed ordinary object pointers [true]
[2025-06-24T12:13:31,285][INFO ][o.e.n.Node               ] [seo-node-1] node name [seo-node-1], node ID [bmhpVyXASJ2JME1Pah6BeQ], cluster name [ai-seo-king], roles [transform, data_frozen, master, remote_cluster_client, data, ml, data_content, data_hot, data_warm, data_cold, ingest]
[2025-06-24T12:13:33,405][INFO ][o.e.x.m.p.l.CppLogMessageHandler] [seo-node-1] [controller/92403] [Main.cc@122] controller (64 bit): Version 7.17.15 (Build 8285074a0b4035) Copyright (c) 2023 Elasticsearch BV
[2025-06-24T12:13:33,639][INFO ][o.e.i.g.ConfigDatabases  ] [seo-node-1] initialized default databases [[GeoLite2-Country.mmdb, GeoLite2-City.mmdb, GeoLite2-ASN.mmdb]], config databases [[]] and watching [/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/config/ingest-geoip] for changes
[2025-06-24T12:13:33,640][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] initialized database registry, using geoip-databases directory [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ]
[2025-06-24T12:13:33,913][INFO ][o.e.t.NettyAllocator     ] [seo-node-1] creating NettyAllocator with the following configs: [name=elasticsearch_configured, chunk_size=1mb, suggested_max_allocation_size=1mb, factors={es.unsafe.use_netty_default_chunk_and_page_size=false, g1gc_enabled=true, g1gc_region_size=4mb}]
[2025-06-24T12:13:33,931][INFO ][o.e.i.r.RecoverySettings ] [seo-node-1] using rate limit [40mb] with [default=40mb, read=0b, write=0b, max=0b]
[2025-06-24T12:13:33,949][INFO ][o.e.d.DiscoveryModule    ] [seo-node-1] using discovery type [single-node] and seed hosts providers [settings]
[2025-06-24T12:13:34,198][INFO ][o.e.g.DanglingIndicesState] [seo-node-1] gateway.auto_import_dangling_indices is disabled, dangling indices will not be automatically detected or imported and must be managed manually
[2025-06-24T12:13:34,506][INFO ][o.e.n.Node               ] [seo-node-1] initialized
[2025-06-24T12:13:34,506][INFO ][o.e.n.Node               ] [seo-node-1] starting ...
[2025-06-24T12:13:34,534][INFO ][o.e.x.s.c.f.PersistentCache] [seo-node-1] persistent cache index loaded
[2025-06-24T12:13:34,535][INFO ][o.e.x.d.l.DeprecationIndexingComponent] [seo-node-1] deprecation component started
[2025-06-24T12:13:34,597][INFO ][o.e.t.TransportService   ] [seo-node-1] publish_address {*************:9300}, bound_addresses {[::]:9300}
[2025-06-24T12:13:34,604][INFO ][o.e.x.m.Monitoring       ] [seo-node-1] creating template [.monitoring-alerts-7] with version [7]
[2025-06-24T12:13:34,607][INFO ][o.e.x.m.Monitoring       ] [seo-node-1] creating template [.monitoring-es] with version [7]
[2025-06-24T12:13:34,608][INFO ][o.e.x.m.Monitoring       ] [seo-node-1] creating template [.monitoring-kibana] with version [7]
[2025-06-24T12:13:34,610][INFO ][o.e.x.m.Monitoring       ] [seo-node-1] creating template [.monitoring-logstash] with version [7]
[2025-06-24T12:13:34,612][INFO ][o.e.x.m.Monitoring       ] [seo-node-1] creating template [.monitoring-beats] with version [7]
[2025-06-24T12:13:34,714][INFO ][o.e.c.c.Coordinator      ] [seo-node-1] setting initial configuration to VotingConfiguration{bmhpVyXASJ2JME1Pah6BeQ}
[2025-06-24T12:13:34,823][INFO ][o.e.c.s.MasterService    ] [seo-node-1] elected-as-master ([1] nodes joined)[{seo-node-1}{bmhpVyXASJ2JME1Pah6BeQ}{60RTttZ5Rc2TppAYjTVxqw}{*************}{*************:9300}{cdfhilmrstw} elect leader, _BECOME_MASTER_TASK_, _FINISH_ELECTION_], term: 1, version: 1, delta: master node changed {previous [], current [{seo-node-1}{bmhpVyXASJ2JME1Pah6BeQ}{60RTttZ5Rc2TppAYjTVxqw}{*************}{*************:9300}{cdfhilmrstw}]}
[2025-06-24T12:13:34,855][INFO ][o.e.c.c.CoordinationState] [seo-node-1] cluster UUID set to [HuIFY4U4RMeVvBeL4rpyPw]
[2025-06-24T12:13:34,891][INFO ][o.e.c.s.ClusterApplierService] [seo-node-1] master node changed {previous [], current [{seo-node-1}{bmhpVyXASJ2JME1Pah6BeQ}{60RTttZ5Rc2TppAYjTVxqw}{*************}{*************:9300}{cdfhilmrstw}]}, term: 1, version: 1, reason: Publication{term=1, version=1}
[2025-06-24T12:13:34,920][INFO ][o.e.h.AbstractHttpServerTransport] [seo-node-1] publish_address {*************:9200}, bound_addresses {[::]:9200}
[2025-06-24T12:13:34,920][INFO ][o.e.n.Node               ] [seo-node-1] started
[2025-06-24T12:13:34,976][INFO ][o.e.g.GatewayService     ] [seo-node-1] recovered [0] indices into cluster_state
[2025-06-24T12:13:35,436][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [.ml-anomalies-] for index patterns [.ml-anomalies-*]
[2025-06-24T12:13:35,492][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [.ml-state] for index patterns [.ml-state*]
[2025-06-24T12:13:35,547][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [.ml-stats] for index patterns [.ml-stats-*]
[2025-06-24T12:13:35,600][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [.ml-notifications-000002] for index patterns [.ml-notifications-000002]
[2025-06-24T12:13:35,655][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding component template [logs-settings]
[2025-06-24T12:13:35,708][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding component template [synthetics-settings]
[2025-06-24T12:13:35,747][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding component template [data-streams-mappings]
[2025-06-24T12:13:35,802][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding component template [metrics-mappings]
[2025-06-24T12:13:35,840][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding component template [logs-mappings]
[2025-06-24T12:13:35,870][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding component template [metrics-settings]
[2025-06-24T12:13:35,901][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding component template [synthetics-mappings]
[2025-06-24T12:13:35,939][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [.watch-history-13] for index patterns [.watcher-history-13*]
[2025-06-24T12:13:35,971][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [ilm-history] for index patterns [ilm-history-5*]
[2025-06-24T12:13:36,004][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding component template [.deprecation-indexing-settings]
[2025-06-24T12:13:36,034][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding component template [.deprecation-indexing-mappings]
[2025-06-24T12:13:36,065][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [.slm-history] for index patterns [.slm-history-5*]
[2025-06-24T12:13:36,095][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [logs] for index patterns [logs-*-*]
[2025-06-24T12:13:36,128][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [metrics] for index patterns [metrics-*-*]
[2025-06-24T12:13:36,162][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [synthetics] for index patterns [synthetics-*-*]
[2025-06-24T12:13:36,191][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [.deprecation-indexing-template] for index patterns [.logs-deprecation.*]
[2025-06-24T12:13:36,220][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [ml-size-based-ilm-policy]
[2025-06-24T12:13:36,255][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [logs]
[2025-06-24T12:13:36,283][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [metrics]
[2025-06-24T12:13:36,311][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [synthetics]
[2025-06-24T12:13:36,339][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [7-days-default]
[2025-06-24T12:13:36,373][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [365-days-default]
[2025-06-24T12:13:36,417][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [30-days-default]
[2025-06-24T12:13:36,455][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [180-days-default]
[2025-06-24T12:13:36,484][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [90-days-default]
[2025-06-24T12:13:36,510][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [watch-history-ilm-policy]
[2025-06-24T12:13:36,538][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [ilm-history-ilm-policy]
[2025-06-24T12:13:36,566][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [slm-history-ilm-policy]
[2025-06-24T12:13:36,591][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [.deprecation-indexing-ilm-policy]
[2025-06-24T12:13:36,618][INFO ][o.e.x.i.a.TransportPutLifecycleAction] [seo-node-1] adding index lifecycle policy [.fleet-actions-results-ilm-policy]
[2025-06-24T12:13:36,677][INFO ][o.e.i.g.GeoIpDownloader  ] [seo-node-1] updating geoip databases
[2025-06-24T12:13:36,678][INFO ][o.e.i.g.GeoIpDownloader  ] [seo-node-1] fetching geoip databases overview from [https://geoip.elastic.co/v1/database?elastic_geoip_service_tos=agree]
[2025-06-24T12:13:36,730][INFO ][o.e.l.LicenseService     ] [seo-node-1] license [41a2d8a7-d3ee-4040-b3a2-1d1cbb9186bb] mode [basic] - valid
[2025-06-24T12:13:37,744][INFO ][o.e.c.m.MetadataCreateIndexService] [seo-node-1] [.geoip_databases] creating index, cause [auto(bulk api)], templates [], shards [1]/[0]
[2025-06-24T12:13:37,931][INFO ][o.e.c.r.a.AllocationService] [seo-node-1] Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[.geoip_databases][0]]]).
[2025-06-24T12:13:38,725][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] retrieve geoip database [GeoLite2-ASN.mmdb] from [.geoip_databases] to [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-ASN.mmdb.tmp.gz]
[2025-06-24T12:13:38,731][INFO ][o.e.i.g.GeoIpDownloader  ] [seo-node-1] successfully downloaded geoip database [GeoLite2-ASN.mmdb]
[2025-06-24T12:13:38,843][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] successfully reloaded changed geoip database file [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-ASN.mmdb]
[2025-06-24T12:13:44,186][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] retrieve geoip database [GeoLite2-City.mmdb] from [.geoip_databases] to [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-City.mmdb.tmp.gz]
[2025-06-24T12:13:44,193][INFO ][o.e.i.g.GeoIpDownloader  ] [seo-node-1] successfully downloaded geoip database [GeoLite2-City.mmdb]
[2025-06-24T12:13:44,630][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] successfully reloaded changed geoip database file [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-City.mmdb]
[2025-06-24T12:13:45,249][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] retrieve geoip database [GeoLite2-Country.mmdb] from [.geoip_databases] to [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-Country.mmdb.tmp.gz]
[2025-06-24T12:13:45,256][INFO ][o.e.i.g.GeoIpDownloader  ] [seo-node-1] successfully downloaded geoip database [GeoLite2-Country.mmdb]
[2025-06-24T12:13:45,318][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] successfully reloaded changed geoip database file [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-Country.mmdb]
[2025-06-24T12:15:14,196][INFO ][o.e.c.m.MetadataIndexTemplateService] [seo-node-1] adding index template [seo_template] for index patterns [seo_*]
[2025-06-24T12:15:22,056][INFO ][o.e.c.m.MetadataCreateIndexService] [seo-node-1] [seo_content] creating index, cause [api], templates [seo_template], shards [1]/[0]
[2025-06-24T12:15:22,186][INFO ][o.e.c.r.a.AllocationService] [seo-node-1] Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[seo_content][0]]]).
[2025-06-24T12:15:22,233][INFO ][o.e.c.m.MetadataCreateIndexService] [seo-node-1] [seo_analysis] creating index, cause [api], templates [seo_template], shards [1]/[0]
[2025-06-24T12:15:22,359][INFO ][o.e.c.r.a.AllocationService] [seo-node-1] Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[seo_analysis][0]]]).
[2025-06-24T12:15:22,405][INFO ][o.e.c.m.MetadataCreateIndexService] [seo-node-1] [seo_keywords] creating index, cause [api], templates [seo_template], shards [1]/[0]
[2025-06-24T12:15:22,529][INFO ][o.e.c.r.a.AllocationService] [seo-node-1] Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[seo_keywords][0]]]).
[2025-06-24T12:15:22,575][INFO ][o.e.c.m.MetadataCreateIndexService] [seo-node-1] [seo_competitors] creating index, cause [api], templates [seo_template], shards [1]/[0]
[2025-06-24T12:15:22,699][INFO ][o.e.c.r.a.AllocationService] [seo-node-1] Cluster health status changed from [YELLOW] to [GREEN] (reason: [shards started [[seo_competitors][0]]]).
[2025-06-24T12:31:20,593][INFO ][o.e.x.m.p.NativeController] [seo-node-1] Native controller process has stopped - no new native processes can be started
[2025-06-24T12:31:20,593][INFO ][o.e.n.Node               ] [seo-node-1] stopping ...
[2025-06-24T12:31:20,596][INFO ][o.e.x.w.WatcherService   ] [seo-node-1] stopping watch service, reason [shutdown initiated]
[2025-06-24T12:31:20,597][INFO ][o.e.x.w.WatcherLifeCycleService] [seo-node-1] watcher has stopped and shutdown
[2025-06-24T12:31:20,643][INFO ][o.e.n.Node               ] [seo-node-1] stopped
[2025-06-24T12:31:20,643][INFO ][o.e.n.Node               ] [seo-node-1] closing ...
[2025-06-24T12:31:20,647][INFO ][o.e.i.g.DatabaseReaderLazyLoader] [seo-node-1] evicted [0] entries from cache after reloading database [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-Country.mmdb]
[2025-06-24T12:31:20,647][INFO ][o.e.i.g.DatabaseReaderLazyLoader] [seo-node-1] evicted [0] entries from cache after reloading database [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-ASN.mmdb]
[2025-06-24T12:31:20,647][INFO ][o.e.i.g.DatabaseReaderLazyLoader] [seo-node-1] evicted [0] entries from cache after reloading database [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-11633827965141350891/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-City.mmdb]
[2025-06-24T12:31:20,648][INFO ][o.e.n.Node               ] [seo-node-1] closed
[2025-06-24T12:31:39,682][INFO ][o.e.n.Node               ] [seo-node-1] version[7.17.15], pid[6285], build[default/tar/0b8ecfb4378335f4689c4223d1f1115f16bef3ba/2023-11-10T22:03:46.987399016Z], OS[Mac OS X/15.5/aarch64], JVM[Oracle Corporation/OpenJDK 64-Bit Server VM/21.0.1/21.0.1+12-29]
[2025-06-24T12:31:39,684][INFO ][o.e.n.Node               ] [seo-node-1] JVM home [/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/jdk.app/Contents/Home], using bundled JDK [true]
[2025-06-24T12:31:39,685][INFO ][o.e.n.Node               ] [seo-node-1] JVM arguments [-Xshare:auto, -Des.networkaddress.cache.ttl=60, -Des.networkaddress.cache.negative.ttl=10, -XX:+AlwaysPreTouch, -Xss1m, -Djava.awt.headless=true, -Dfile.encoding=UTF-8, -Djna.nosys=true, -XX:-OmitStackTraceInFastThrow, -XX:+ShowCodeDetailsInExceptionMessages, -Dio.netty.noUnsafe=true, -Dio.netty.noKeySetOptimization=true, -Dio.netty.recycler.maxCapacityPerThread=0, -Dio.netty.allocator.numDirectArenas=0, -Dlog4j.shutdownHookEnabled=false, -Dlog4j2.disable.jmx=true, -Dlog4j2.formatMsgNoLookups=true, -Djava.locale.providers=SPI,COMPAT, --add-opens=java.base/java.io=ALL-UNNAMED, -Djava.security.manager=allow, -XX:+UseG1GC, -Djava.io.tmpdir=/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225, -XX:+HeapDumpOnOutOfMemoryError, -XX:+ExitOnOutOfMemoryError, -XX:HeapDumpPath=data, -XX:ErrorFile=logs/hs_err_pid%p.log, -Xlog:gc*,gc+age=trace,safepoint:file=logs/gc.log:utctime,pid,tags:filecount=32,filesize=64m, -Xms2g, -Xmx2g, -XX:MaxDirectMemorySize=1g, -XX:InitiatingHeapOccupancyPercent=30, -XX:G1ReservePercent=25, -XX:MaxGCPauseMillis=100, -XX:G1HeapRegionSize=4m, -Des.path.home=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15, -Des.path.conf=/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/config, -Des.distribution.flavor=default, -Des.distribution.type=tar, -Des.bundled_jdk=true]
[2025-06-24T12:31:41,029][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [aggs-matrix-stats]
[2025-06-24T12:31:41,029][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [analysis-common]
[2025-06-24T12:31:41,029][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [constant-keyword]
[2025-06-24T12:31:41,030][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [frozen-indices]
[2025-06-24T12:31:41,030][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [ingest-common]
[2025-06-24T12:31:41,030][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [ingest-geoip]
[2025-06-24T12:31:41,030][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [ingest-user-agent]
[2025-06-24T12:31:41,030][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [kibana]
[2025-06-24T12:31:41,030][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [lang-expression]
[2025-06-24T12:31:41,031][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [lang-mustache]
[2025-06-24T12:31:41,031][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [lang-painless]
[2025-06-24T12:31:41,031][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [legacy-geo]
[2025-06-24T12:31:41,031][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [mapper-extras]
[2025-06-24T12:31:41,031][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [mapper-version]
[2025-06-24T12:31:41,031][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [parent-join]
[2025-06-24T12:31:41,031][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [percolator]
[2025-06-24T12:31:41,031][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [rank-eval]
[2025-06-24T12:31:41,031][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [reindex]
[2025-06-24T12:31:41,032][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [repositories-metering-api]
[2025-06-24T12:31:41,032][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [repository-encrypted]
[2025-06-24T12:31:41,032][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [repository-url]
[2025-06-24T12:31:41,032][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [runtime-fields-common]
[2025-06-24T12:31:41,032][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [search-business-rules]
[2025-06-24T12:31:41,032][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [searchable-snapshots]
[2025-06-24T12:31:41,032][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [snapshot-repo-test-kit]
[2025-06-24T12:31:41,032][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [spatial]
[2025-06-24T12:31:41,032][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [transform]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [transport-netty4]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [unsigned-long]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [vector-tile]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [vectors]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [wildcard]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-aggregate-metric]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-analytics]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-async]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-async-search]
[2025-06-24T12:31:41,033][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-autoscaling]
[2025-06-24T12:31:41,034][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ccr]
[2025-06-24T12:31:41,034][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-core]
[2025-06-24T12:31:41,034][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-data-streams]
[2025-06-24T12:31:41,034][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-deprecation]
[2025-06-24T12:31:41,034][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-enrich]
[2025-06-24T12:31:41,034][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-eql]
[2025-06-24T12:31:41,034][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-fleet]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-graph]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-identity-provider]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ilm]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-logstash]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ml]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-monitoring]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-ql]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-rollup]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-security]
[2025-06-24T12:31:41,035][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-shutdown]
[2025-06-24T12:31:41,036][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-sql]
[2025-06-24T12:31:41,036][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-stack]
[2025-06-24T12:31:41,036][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-text-structure]
[2025-06-24T12:31:41,036][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-voting-only-node]
[2025-06-24T12:31:41,036][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded module [x-pack-watcher]
[2025-06-24T12:31:41,037][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded plugin [analysis-icu]
[2025-06-24T12:31:41,037][INFO ][o.e.p.PluginsService     ] [seo-node-1] loaded plugin [analysis-smartcn]
[2025-06-24T12:31:41,057][INFO ][o.e.e.NodeEnvironment    ] [seo-node-1] using [1] data paths, mounts [[/System/Volumes/Data (/dev/disk3s5)]], net usable_space [231gb], net total_space [926.3gb], types [apfs]
[2025-06-24T12:31:41,057][INFO ][o.e.e.NodeEnvironment    ] [seo-node-1] heap size [2gb], compressed ordinary object pointers [true]
[2025-06-24T12:31:41,094][INFO ][o.e.n.Node               ] [seo-node-1] node name [seo-node-1], node ID [bmhpVyXASJ2JME1Pah6BeQ], cluster name [ai-seo-king], roles [transform, data_frozen, master, remote_cluster_client, data, ml, data_content, data_hot, data_warm, data_cold, ingest]
[2025-06-24T12:31:43,087][INFO ][o.e.x.m.p.l.CppLogMessageHandler] [seo-node-1] [controller/6347] [Main.cc@122] controller (64 bit): Version 7.17.15 (Build 8285074a0b4035) Copyright (c) 2023 Elasticsearch BV
[2025-06-24T12:31:43,310][INFO ][o.e.i.g.ConfigDatabases  ] [seo-node-1] initialized default databases [[GeoLite2-Country.mmdb, GeoLite2-City.mmdb, GeoLite2-ASN.mmdb]], config databases [[]] and watching [/Users/<USER>/projects/AISEOking/elasticsearch/elasticsearch-7.17.15/config/ingest-geoip] for changes
[2025-06-24T12:31:43,311][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] initialized database registry, using geoip-databases directory [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ]
[2025-06-24T12:31:43,561][INFO ][o.e.t.NettyAllocator     ] [seo-node-1] creating NettyAllocator with the following configs: [name=elasticsearch_configured, chunk_size=1mb, suggested_max_allocation_size=1mb, factors={es.unsafe.use_netty_default_chunk_and_page_size=false, g1gc_enabled=true, g1gc_region_size=4mb}]
[2025-06-24T12:31:43,576][INFO ][o.e.i.r.RecoverySettings ] [seo-node-1] using rate limit [40mb] with [default=40mb, read=0b, write=0b, max=0b]
[2025-06-24T12:31:43,595][INFO ][o.e.d.DiscoveryModule    ] [seo-node-1] using discovery type [single-node] and seed hosts providers [settings]
[2025-06-24T12:31:43,826][INFO ][o.e.g.DanglingIndicesState] [seo-node-1] gateway.auto_import_dangling_indices is disabled, dangling indices will not be automatically detected or imported and must be managed manually
[2025-06-24T12:31:44,111][INFO ][o.e.n.Node               ] [seo-node-1] initialized
[2025-06-24T12:31:44,111][INFO ][o.e.n.Node               ] [seo-node-1] starting ...
[2025-06-24T12:31:44,137][INFO ][o.e.x.s.c.f.PersistentCache] [seo-node-1] persistent cache index loaded
[2025-06-24T12:31:44,138][INFO ][o.e.x.d.l.DeprecationIndexingComponent] [seo-node-1] deprecation component started
[2025-06-24T12:31:44,204][INFO ][o.e.t.TransportService   ] [seo-node-1] publish_address {*************:9300}, bound_addresses {[::]:9300}
[2025-06-24T12:31:44,392][INFO ][o.e.c.c.Coordinator      ] [seo-node-1] cluster UUID [HuIFY4U4RMeVvBeL4rpyPw]
[2025-06-24T12:31:44,439][INFO ][o.e.c.s.MasterService    ] [seo-node-1] elected-as-master ([1] nodes joined)[{seo-node-1}{bmhpVyXASJ2JME1Pah6BeQ}{xpLzC_5dSq6dijm1eilK1Q}{*************}{*************:9300}{cdfhilmrstw} elect leader, _BECOME_MASTER_TASK_, _FINISH_ELECTION_], term: 2, version: 55, delta: master node changed {previous [], current [{seo-node-1}{bmhpVyXASJ2JME1Pah6BeQ}{xpLzC_5dSq6dijm1eilK1Q}{*************}{*************:9300}{cdfhilmrstw}]}
[2025-06-24T12:31:44,490][INFO ][o.e.c.s.ClusterApplierService] [seo-node-1] master node changed {previous [], current [{seo-node-1}{bmhpVyXASJ2JME1Pah6BeQ}{xpLzC_5dSq6dijm1eilK1Q}{*************}{*************:9300}{cdfhilmrstw}]}, term: 2, version: 55, reason: Publication{term=2, version=55}
[2025-06-24T12:31:44,520][INFO ][o.e.h.AbstractHttpServerTransport] [seo-node-1] publish_address {*************:9200}, bound_addresses {[::]:9200}
[2025-06-24T12:31:44,520][INFO ][o.e.n.Node               ] [seo-node-1] started
[2025-06-24T12:31:44,708][INFO ][o.e.l.LicenseService     ] [seo-node-1] license [41a2d8a7-d3ee-4040-b3a2-1d1cbb9186bb] mode [basic] - valid
[2025-06-24T12:31:44,711][INFO ][o.e.g.GatewayService     ] [seo-node-1] recovered [5] indices into cluster_state
[2025-06-24T12:31:45,348][ERROR][o.e.i.g.GeoIpDownloader  ] [seo-node-1] exception during geoip databases update
org.elasticsearch.ElasticsearchException: not all primary shards of [.geoip_databases] index are active
	at org.elasticsearch.ingest.geoip.GeoIpDownloader.updateDatabases(GeoIpDownloader.java:137) ~[ingest-geoip-7.17.15.jar:7.17.15]
	at org.elasticsearch.ingest.geoip.GeoIpDownloader.runDownloader(GeoIpDownloader.java:284) [ingest-geoip-7.17.15.jar:7.17.15]
	at org.elasticsearch.ingest.geoip.GeoIpDownloaderTaskExecutor.nodeOperation(GeoIpDownloaderTaskExecutor.java:100) [ingest-geoip-7.17.15.jar:7.17.15]
	at org.elasticsearch.ingest.geoip.GeoIpDownloaderTaskExecutor.nodeOperation(GeoIpDownloaderTaskExecutor.java:46) [ingest-geoip-7.17.15.jar:7.17.15]
	at org.elasticsearch.persistent.NodePersistentTasksExecutor$1.doRun(NodePersistentTasksExecutor.java:42) [elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.common.util.concurrent.ThreadContext$ContextPreservingAbstractRunnable.doRun(ThreadContext.java:777) [elasticsearch-7.17.15.jar:7.17.15]
	at org.elasticsearch.common.util.concurrent.AbstractRunnable.run(AbstractRunnable.java:26) [elasticsearch-7.17.15.jar:7.17.15]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) [?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) [?:?]
	at java.lang.Thread.run(Thread.java:1583) [?:?]
[2025-06-24T12:31:45,387][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] retrieve geoip database [GeoLite2-Country.mmdb] from [.geoip_databases] to [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-Country.mmdb.tmp.gz]
[2025-06-24T12:31:45,392][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] retrieve geoip database [GeoLite2-City.mmdb] from [.geoip_databases] to [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-City.mmdb.tmp.gz]
[2025-06-24T12:31:45,393][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] retrieve geoip database [GeoLite2-ASN.mmdb] from [.geoip_databases] to [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-ASN.mmdb.tmp.gz]
[2025-06-24T12:31:45,513][INFO ][o.e.c.r.a.AllocationService] [seo-node-1] Cluster health status changed from [RED] to [GREEN] (reason: [shards started [[seo_content][0]]]).
[2025-06-24T12:31:45,658][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] successfully reloaded changed geoip database file [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-Country.mmdb]
[2025-06-24T12:31:45,666][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] successfully reloaded changed geoip database file [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-ASN.mmdb]
[2025-06-24T12:31:45,995][INFO ][o.e.i.g.DatabaseNodeService] [seo-node-1] successfully reloaded changed geoip database file [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-City.mmdb]
[2025-06-24T12:48:02,670][INFO ][o.e.c.m.MetadataCreateIndexService] [seo-node-1] [test_diagnostic_index] creating index, cause [auto(bulk api)], templates [], shards [1]/[1]
[2025-06-24T12:48:02,853][INFO ][o.e.c.m.MetadataMappingService] [seo-node-1] [test_diagnostic_index/myCQkgyTSb-k_UawQXOEfg] create_mapping [_doc]
[2025-06-24T12:48:02,932][INFO ][o.e.c.m.MetadataDeleteIndexService] [seo-node-1] [test_diagnostic_index/myCQkgyTSb-k_UawQXOEfg] deleting index
[2025-06-24T12:48:31,244][INFO ][o.e.c.m.MetadataCreateIndexService] [seo-node-1] [test_diagnostic_index] creating index, cause [auto(bulk api)], templates [], shards [1]/[1]
[2025-06-24T12:48:31,418][INFO ][o.e.c.m.MetadataMappingService] [seo-node-1] [test_diagnostic_index/UYJ5gAuZTZGSBO9ISXeL7Q] create_mapping [_doc]
[2025-06-24T12:48:31,500][INFO ][o.e.c.m.MetadataDeleteIndexService] [seo-node-1] [test_diagnostic_index/UYJ5gAuZTZGSBO9ISXeL7Q] deleting index
[2025-06-24T12:51:05,254][INFO ][o.e.c.m.MetadataMappingService] [seo-node-1] [seo_content/QoZJp3ugQp2isC7j8mkqGg] update_mapping [_doc]
[2025-06-24T12:54:46,459][INFO ][o.e.c.m.MetadataCreateIndexService] [seo-node-1] [test_diagnostic_index] creating index, cause [auto(bulk api)], templates [], shards [1]/[1]
[2025-06-24T12:54:46,635][INFO ][o.e.c.m.MetadataMappingService] [seo-node-1] [test_diagnostic_index/IZOyy77QQCy8lbk8pTHgtw] create_mapping [_doc]
[2025-06-24T12:54:46,709][INFO ][o.e.c.m.MetadataDeleteIndexService] [seo-node-1] [test_diagnostic_index/IZOyy77QQCy8lbk8pTHgtw] deleting index
[2025-06-24T12:57:55,591][INFO ][o.e.c.m.MetadataMappingService] [seo-node-1] [seo_keywords/RGC1t6JrTIuDc6ufrylX2w] update_mapping [_doc]
[2025-06-24T13:00:30,803][INFO ][o.e.c.m.MetadataMappingService] [seo-node-1] [seo_competitors/4vMgpTtFRQOpXkcx_hAENA] update_mapping [_doc]
[2025-06-24T13:01:05,979][INFO ][o.e.c.m.MetadataMappingService] [seo-node-1] [seo_analysis/kCn6vXbuQ3GiZft7E_6PkQ] update_mapping [_doc]
[2025-06-24T13:24:48,711][INFO ][o.e.x.m.p.NativeController] [seo-node-1] Native controller process has stopped - no new native processes can be started
[2025-06-24T13:24:48,712][INFO ][o.e.n.Node               ] [seo-node-1] stopping ...
[2025-06-24T13:24:48,716][INFO ][o.e.x.w.WatcherService   ] [seo-node-1] stopping watch service, reason [shutdown initiated]
[2025-06-24T13:24:48,717][INFO ][o.e.x.w.WatcherLifeCycleService] [seo-node-1] watcher has stopped and shutdown
[2025-06-24T13:24:48,910][INFO ][o.e.n.Node               ] [seo-node-1] stopped
[2025-06-24T13:24:48,910][INFO ][o.e.n.Node               ] [seo-node-1] closing ...
[2025-06-24T13:24:48,915][INFO ][o.e.i.g.DatabaseReaderLazyLoader] [seo-node-1] evicted [0] entries from cache after reloading database [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-Country.mmdb]
[2025-06-24T13:24:48,915][INFO ][o.e.i.g.DatabaseReaderLazyLoader] [seo-node-1] evicted [0] entries from cache after reloading database [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-ASN.mmdb]
[2025-06-24T13:24:48,915][INFO ][o.e.i.g.DatabaseReaderLazyLoader] [seo-node-1] evicted [0] entries from cache after reloading database [/var/folders/sq/gvbn1qzn7f50l2hyspn67zmw0000gn/T/elasticsearch-4085757879147952225/geoip-databases/bmhpVyXASJ2JME1Pah6BeQ/GeoLite2-City.mmdb]
[2025-06-24T13:24:48,919][INFO ][o.e.n.Node               ] [seo-node-1] closed
