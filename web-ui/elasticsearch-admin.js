// Elasticsearch 管理界面 JavaScript
(function() {
    'use strict';
    
    // 避免重複初始化
    if (window.ElasticsearchAdmin) {
        return;
    }

    class ElasticsearchAdmin {
        constructor() {
            this.esUrl = 'http://localhost:9200';
            this.refreshInterval = 30000;
            this.charts = {};
            this.refreshTimer = null;
            this.indices = [];
            
            this.init();
        }

        init() {
            this.setupEventListeners();
            this.loadClusterInfo();
            this.loadIndices();
            this.showSection('dashboard');
        }

        setupEventListeners() {
            const searchType = document.getElementById('search-type');
            if (searchType) {
                searchType.addEventListener('change', () => {
                    this.updateSearchParams();
                });
            }
        }

        showSection(sectionName) {
            // 隱藏所有區域
            document.querySelectorAll('.section-content').forEach(section => {
                section.classList.add('hidden');
            });

            // 移除導航活動狀態
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('bg-blue-50', 'text-blue-600');
            });

            // 顯示選中區域
            const targetSection = document.getElementById(`${sectionName}-section`);
            if (targetSection) {
                targetSection.classList.remove('hidden');
            }

            // 激活導航項
            const navItem = document.querySelector(`a[href="#${sectionName}"]`);
            if (navItem) {
                navItem.classList.add('bg-blue-50', 'text-blue-600');
            }

            // 載入對應數據
            switch(sectionName) {
                case 'dashboard':
                    this.loadDashboardData();
                    break;
                case 'indices':
                    this.loadIndicesTable();
                    break;
                case 'search':
                    this.loadSearchIndices();
                    break;
                case 'analytics':
                    this.loadAnalytics();
                    break;
            }
        }

        async loadClusterInfo() {
            try {
                this.showLoading();
                
                const healthResponse = await fetch(`${this.esUrl}/_cluster/health`);
                const health = await healthResponse.json();
                
                const statsResponse = await fetch(`${this.esUrl}/_cluster/stats`);
                const stats = await statsResponse.json();

                this.updateClusterStatus(health);
                this.updateClusterStats(stats);

            } catch (error) {
                console.error('載入集群信息失敗:', error);
                this.updateConnectionStatus(false);
            } finally {
                this.hideLoading();
            }
        }

        updateClusterStatus(health) {
            const statusText = document.getElementById('status-text');
            const healthElement = document.getElementById('cluster-health');
            const statusIndicator = document.querySelector('#cluster-status .status-indicator');
            
            if (statusText) statusText.textContent = `集群: ${health.cluster_name}`;
            if (healthElement) healthElement.textContent = health.status.toUpperCase();

            if (statusIndicator) {
                statusIndicator.className = 'status-indicator';
                switch(health.status) {
                    case 'green':
                        statusIndicator.classList.add('status-green');
                        break;
                    case 'yellow':
                        statusIndicator.classList.add('status-yellow');
                        break;
                    case 'red':
                        statusIndicator.classList.add('status-red');
                        break;
                }
            }

            this.updateConnectionStatus(true);
        }

        updateClusterStats(stats) {
            const elements = {
                'indices-count': stats.indices.count || 0,
                'docs-count': this.formatNumber(stats.indices.docs.count || 0),
                'store-size': this.formatBytes(stats.indices.store.size_in_bytes || 0)
            };

            Object.entries(elements).forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) element.textContent = value;
            });
        }

        async loadIndices() {
            try {
                const response = await fetch(`${this.esUrl}/_cat/indices?format=json&bytes=b`);
                const indices = await response.json();
                
                this.indices = indices;
                this.updateSEOIndicesOverview(indices);
                
            } catch (error) {
                console.error('載入索引失敗:', error);
            }
        }

        updateSEOIndicesOverview(indices) {
            const seoIndices = ['seo_content', 'seo_analysis', 'seo_keywords', 'seo_competitors'];
            const container = document.getElementById('seo-indices-overview');
            
            if (!container) return;

            const descriptions = {
                'seo_content': { icon: 'fas fa-file-alt', name: 'SEO 內容', desc: '網頁內容和 SEO 數據' },
                'seo_analysis': { icon: 'fas fa-chart-line', name: 'SEO 分析', desc: 'SEO 分析結果和建議' },
                'seo_keywords': { icon: 'fas fa-key', name: 'SEO 關鍵詞', desc: '關鍵詞研究數據' },
                'seo_competitors': { icon: 'fas fa-users', name: 'SEO 競爭對手', desc: '競爭對手分析數據' }
            };

            container.innerHTML = seoIndices.map(indexName => {
                const index = indices.find(i => i.index === indexName);
                const desc = descriptions[indexName];
                
                if (!index) {
                    return `
                        <div class="bg-gray-100 rounded-lg p-4 text-center">
                            <i class="${desc.icon} text-3xl text-gray-400 mb-2"></i>
                            <h4 class="font-semibold text-gray-600">${desc.name}</h4>
                            <p class="text-sm text-gray-500 mb-2">${desc.desc}</p>
                            <span class="text-sm text-red-600">未創建</span>
                        </div>
                    `;
                }

                return `
                    <div class="bg-white border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="admin.viewIndexDetails('${indexName}')">
                        <div class="flex items-center justify-between mb-2">
                            <i class="${desc.icon} text-2xl text-blue-600"></i>
                            <span class="status-indicator status-${index.health || 'green'}"></span>
                        </div>
                        <h4 class="font-semibold text-gray-900">${desc.name}</h4>
                        <p class="text-sm text-gray-600 mb-2">${desc.desc}</p>
                        <div class="text-sm space-y-1">
                            <div class="flex justify-between">
                                <span class="text-gray-500">文檔:</span>
                                <span class="font-medium">${this.formatNumber(index['docs.count'] || 0)}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-500">大小:</span>
                                <span class="font-medium">${this.formatBytes(this.parseBytes(index['store.size']) || 0)}</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        loadDashboardData() {
            if (this.indices && this.indices.length > 0) {
                this.updateDocsChart();
                this.updateStorageChart();
            }
        }

        updateDocsChart() {
            const canvas = document.getElementById('docsChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const seoIndices = this.indices.filter(i => i.index.startsWith('seo_'));
            
            if (seoIndices.length === 0) return;

            const labels = seoIndices.map(i => i.index.replace('seo_', '').toUpperCase());
            const data = seoIndices.map(i => parseInt(i['docs.count']) || 0);

            if (this.charts.docs) {
                this.charts.docs.destroy();
            }

            this.charts.docs = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: ['#3B82F6', '#8B5CF6', '#10B981', '#F59E0B']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        updateStorageChart() {
            const canvas = document.getElementById('storageChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const seoIndices = this.indices.filter(i => i.index.startsWith('seo_'));
            
            if (seoIndices.length === 0) return;

            const labels = seoIndices.map(i => i.index.replace('seo_', '').toUpperCase());
            const data = seoIndices.map(i => {
                const bytes = this.parseBytes(i['store.size']);
                return bytes / 1024 / 1024; // 轉換為MB
            });

            if (this.charts.storage) {
                this.charts.storage.destroy();
            }

            this.charts.storage = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '存儲大小 (MB)',
                        data: data,
                        backgroundColor: '#3B82F6'
                    }]
                },
                options: {
                    responsive: true,
                    scales: { y: { beginAtZero: true } }
                }
            });
        }

        async loadIndicesTable() {
            try {
                const response = await fetch(`${this.esUrl}/_cat/indices?format=json&bytes=b&s=index`);
                const indices = await response.json();
                
                const tableContainer = document.getElementById('indices-table');
                if (!tableContainer) return;
                
                const table = `
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">索引名稱</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">狀態</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">文檔數</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">大小</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${indices.map(index => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${index.index}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(index.health)}">
                                            ${(index.health || 'unknown').toUpperCase()}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${this.formatNumber(index['docs.count'] || 0)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${this.formatBytes(this.parseBytes(index['store.size']) || 0)}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="admin.viewIndexDetails('${index.index}')" class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                                        <button onclick="admin.searchInIndex('${index.index}')" class="text-green-600 hover:text-green-900">搜索</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
                
                tableContainer.innerHTML = table;
                
            } catch (error) {
                console.error('載入索引表格失敗:', error);
            }
        }

        loadSearchIndices() {
            const select = document.getElementById('search-index');
            if (!select) return;
            
            select.innerHTML = '<option value="">選擇索引...</option>' + 
                this.indices.map(index => 
                    `<option value="${index.index}">${index.index}</option>`
                ).join('');
            
            this.updateSearchParams();
        }

        updateSearchParams() {
            const searchType = document.getElementById('search-type')?.value || 'match_all';
            const paramsContainer = document.getElementById('search-params');
            if (!paramsContainer) return;
            
            let paramsHTML = '';
            
            switch(searchType) {
                case 'match':
                    paramsHTML = `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">搜索字段</label>
                            <input type="text" id="search-field" placeholder="title" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">搜索值</label>
                            <input type="text" id="search-value" placeholder="搜索內容" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                        </div>
                    `;
                    break;
                case 'custom':
                    paramsHTML = `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">自定義查詢 JSON</label>
                            <textarea id="custom-query" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder='{"query": {"match": {"title": "SEO"}}}'></textarea>
                        </div>
                    `;
                    break;
            }
            
            paramsContainer.innerHTML = paramsHTML;
        }

        async executeSearch() {
            const indexName = document.getElementById('search-index')?.value;
            const searchType = document.getElementById('search-type')?.value || 'match_all';
            
            if (!indexName) {
                alert('請選擇索引');
                return;
            }
            
            let query = { query: { match_all: {} }, size: 10 };
            
            try {
                if (searchType === 'match') {
                    const field = document.getElementById('search-field')?.value;
                    const value = document.getElementById('search-value')?.value;
                    if (field && value) {
                        query.query = { match: { [field]: value } };
                    }
                } else if (searchType === 'custom') {
                    const customQuery = document.getElementById('custom-query')?.value;
                    if (customQuery) {
                        query = JSON.parse(customQuery);
                    }
                }
                
                this.showLoading();
                
                const response = await fetch(`${this.esUrl}/${indexName}/_search`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(query)
                });
                
                const results = await response.json();
                this.displaySearchResults(results);
                
            } catch (error) {
                console.error('搜索失敗:', error);
                alert('搜索失敗: ' + error.message);
            } finally {
                this.hideLoading();
            }
        }

        displaySearchResults(results) {
            const container = document.getElementById('search-results');
            if (!container) return;
            
            if (results.hits && results.hits.hits.length > 0) {
                const hits = results.hits.hits;
                const total = results.hits.total?.value || results.hits.total || 0;
                
                container.innerHTML = `
                    <div class="mb-4">
                        <p class="text-sm text-gray-600">找到 ${total} 條結果，顯示前 ${hits.length} 條</p>
                    </div>
                    <div class="space-y-4">
                        ${hits.map((hit, index) => `
                            <div class="border rounded-lg p-4">
                                <div class="flex justify-between items-start mb-2">
                                    <h4 class="font-medium text-gray-900">文檔 #${index + 1}</h4>
                                    <span class="text-xs text-gray-500">分數: ${hit._score}</span>
                                </div>
                                <div class="text-sm text-gray-600 mb-2">
                                    <strong>ID:</strong> ${hit._id}
                                </div>
                                <div class="json-viewer text-xs">
                                    ${JSON.stringify(hit._source, null, 2)}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="flex items-center justify-center h-32 text-gray-500">
                        <div class="text-center">
                            <i class="fas fa-search text-3xl mb-2"></i>
                            <p>沒有找到相關結果</p>
                        </div>
                    </div>
                `;
            }
        }

        viewIndexDetails(indexName) {
            alert(`查看索引詳情: ${indexName}\n\n這裡可以實現詳細的索引信息展示`);
        }

        searchInIndex(indexName) {
            this.showSection('search');
            const searchIndex = document.getElementById('search-index');
            if (searchIndex) {
                searchIndex.value = indexName;
            }
        }

        loadAnalytics() {
            // 模擬分析數據
            this.setupSEOContentChart();
            this.setupKeywordsChart();
        }

        setupSEOContentChart() {
            const canvas = document.getElementById('seoContentChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            if (this.charts.seoContent) {
                this.charts.seoContent.destroy();
            }

            this.charts.seoContent = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: 'SEO 內容增長',
                        data: [120, 190, 300, 500, 720, 890],
                        borderColor: '#3B82F6',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: { y: { beginAtZero: true } }
                }
            });
        }

        setupKeywordsChart() {
            const canvas = document.getElementById('keywordsChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            if (this.charts.keywords) {
                this.charts.keywords.destroy();
            }

            this.charts.keywords = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: ['高流量', '中流量', '低流量', '長尾詞', '品牌詞'],
                    datasets: [{
                        label: '關鍵詞分布',
                        data: [80, 60, 40, 90, 70],
                        borderColor: '#8B5CF6',
                        backgroundColor: 'rgba(139, 92, 246, 0.2)'
                    }]
                },
                options: { responsive: true }
            });
        }

        refreshAll() {
            this.loadClusterInfo();
            this.loadIndices();
            
            const activeSection = document.querySelector('.section-content:not(.hidden)');
            if (activeSection) {
                const sectionId = activeSection.id.replace('-section', '');
                this.showSection(sectionId);
            }
        }

        updateConnectionStatus(connected) {
            const statusIndicator = document.querySelector('#cluster-status .status-indicator');
            const statusText = document.getElementById('status-text');
            const healthElement = document.getElementById('cluster-health');
            
            if (statusIndicator) {
                statusIndicator.className = 'status-indicator';
                statusIndicator.classList.add(connected ? 'status-green' : 'status-red');
            }
            
            if (!connected) {
                if (statusText) statusText.textContent = '連接失敗';
                if (healthElement) healthElement.textContent = 'ERROR';
            }
        }

        formatNumber(num) {
            return new Intl.NumberFormat('zh-TW').format(num);
        }

        formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        parseBytes(sizeStr) {
            if (!sizeStr) return 0;
            
            const units = { 'b': 1, 'kb': 1024, 'mb': 1024 * 1024, 'gb': 1024 * 1024 * 1024 };
            const match = sizeStr.toLowerCase().match(/^(\d+(?:\.\d+)?)\s*([a-z]+)$/);
            
            if (match) {
                const value = parseFloat(match[1]);
                const unit = match[2];
                return value * (units[unit] || 1);
            }
            
            return parseInt(sizeStr) || 0;
        }

        getStatusClass(health) {
            switch(health) {
                case 'green': return 'bg-green-100 text-green-800';
                case 'yellow': return 'bg-yellow-100 text-yellow-800';
                case 'red': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        showLoading() {
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.classList.remove('hidden');
                overlay.classList.add('flex');
            }
        }

        hideLoading() {
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.classList.add('hidden');
                overlay.classList.remove('flex');
            }
        }

        saveSettings() {
            const esUrl = document.getElementById('es-url')?.value;
            const refreshInterval = parseInt(document.getElementById('refresh-interval')?.value) * 1000;
            
            if (esUrl) this.esUrl = esUrl;
            if (refreshInterval) this.refreshInterval = refreshInterval;
            
            alert('設置已保存');
        }

        testConnection() {
            this.loadClusterInfo().then(() => {
                alert('連接測試成功');
            }).catch(() => {
                alert('連接測試失敗');
            });
        }
    }

    // 全局函數
    window.toggleSidebar = function() {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.toggle('hidden');
        }
    };

    window.showSection = function(sectionName) {
        if (window.admin) {
            window.admin.showSection(sectionName);
        }
    };

    window.refreshAll = function() {
        if (window.admin) {
            window.admin.refreshAll();
        }
    };

    window.executeSearch = function() {
        if (window.admin) {
            window.admin.executeSearch();
        }
    };

    window.saveSettings = function() {
        if (window.admin) {
            window.admin.saveSettings();
        }
    };

    window.testConnection = function() {
        if (window.admin) {
            window.admin.testConnection();
        }
    };

    // 將 ElasticsearchAdmin 類暴露到全局
    window.ElasticsearchAdmin = ElasticsearchAdmin;

    // 初始化
    document.addEventListener('DOMContentLoaded', () => {
        window.admin = new ElasticsearchAdmin();
    });
})(); 